"""
Face-BMI模型优化前后对比评估脚本
"""

import torch
import torch.nn as nn
import numpy as np
import json
import matplotlib.pyplot as plt
from pathlib import Path
import time
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体（支持中文）
plt.rcParams['axes.unicode_minus'] = False    # 正确显示负号

# 模拟原始模型结构
class OriginalBMIPredictor(nn.Module):
    """原始BMI预测模型（简化版）"""
    
    def __init__(self):
        super().__init__()
        # 简化的特征提取器
        self.feature_extractor = nn.Sequential(
            nn.Conv2d(3, 64, 3, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(2),
            nn.Conv2d(64, 128, 3, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(2),
            nn.AdaptiveAvgPool2d(1),
            nn.<PERSON><PERSON>(),
            nn.Linear(128, 512)
        )
        
        # 特征融合（简单拼接）
        self.feature_fusion = nn.Sequential(
            nn.Linear(1024, 512),
            nn.ReLU(),
            nn.Dropout(0.5)
        )
        
        # 关系层
        self.relation_layer = nn.Sequential(
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, 128),
            nn.ReLU()
        )
        
        # 预测头
        self.start_bmi_head = nn.Linear(128, 1)
        self.end_bmi_head = nn.Linear(128, 1)
        self.bmi_change_head = nn.Linear(128, 1)
        
        # 固定权重的损失函数
        self.criterion = nn.MSELoss()
        
    def forward(self, x):
        before_img = x['before_image']
        after_img = x['after_image']
        
        # 特征提取
        before_features = self.feature_extractor(before_img)
        after_features = self.feature_extractor(after_img)
        
        # 简单拼接
        combined_features = torch.cat([before_features, after_features], dim=1)
        fused_features = self.feature_fusion(combined_features)
        relation_features = self.relation_layer(fused_features)
        
        # 预测
        start_bmi = self.start_bmi_head(relation_features).squeeze()
        end_bmi = self.end_bmi_head(relation_features).squeeze()
        bmi_change = self.bmi_change_head(relation_features).squeeze()
        
        return {
            'start_bmi': start_bmi,
            'end_bmi': end_bmi,
            'bmi_change': bmi_change
        }
    
    def compute_loss(self, predictions, targets):
        """原始固定权重损失"""
        start_loss = self.criterion(predictions['start_bmi'], targets['start_bmi'])
        end_loss = self.criterion(predictions['end_bmi'], targets['end_bmi'])
        change_loss = self.criterion(predictions['bmi_change'], targets['bmi_change'])
        
        # 固定权重 1:1:0.5
        total_loss = start_loss + end_loss + 0.5 * change_loss
        
        return {
            'total_loss': total_loss,
            'start_loss': start_loss,
            'end_loss': end_loss,
            'change_loss': change_loss
        }


# 模拟增强模型结构
class EnhancedBMIPredictor(nn.Module):
    """增强BMI预测模型（简化版）"""
    
    def __init__(self):
        super().__init__()
        # 增强的特征提取器（多尺度）
        self.feature_extractor = nn.Sequential(
            nn.Conv2d(3, 64, 3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(),
            nn.MaxPool2d(2),
            nn.Conv2d(64, 128, 3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(),
            nn.MaxPool2d(2),
            nn.Conv2d(128, 256, 3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(),
            nn.AdaptiveAvgPool2d(1),
            nn.Flatten(),
            nn.Linear(256, 512)
        )
        
        # 交叉注意力机制（简化版）
        self.cross_attention = nn.MultiheadAttention(512, 8, batch_first=True)
        
        # 增强的特征融合
        self.feature_fusion = nn.Sequential(
            nn.Linear(1024, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU()
        )
        
        # 关系层
        self.relation_layer = nn.Sequential(
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 共享表示
        self.shared_head = nn.Sequential(
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 任务特定头
        self.start_bmi_head = nn.Sequential(nn.Linear(64, 32), nn.ReLU(), nn.Linear(32, 1))
        self.end_bmi_head = nn.Sequential(nn.Linear(64, 32), nn.ReLU(), nn.Linear(32, 1))
        self.bmi_change_head = nn.Sequential(nn.Linear(64, 32), nn.ReLU(), nn.Linear(32, 1))
        
        # 动态任务权重
        self.log_vars = nn.Parameter(torch.zeros(3))
        
        # 鲁棒损失函数
        self.huber_loss = nn.HuberLoss(delta=1.0)
        
    def forward(self, x):
        before_img = x['before_image']
        after_img = x['after_image']
        
        # 特征提取
        before_features = self.feature_extractor(before_img)
        after_features = self.feature_extractor(after_img)
        
        # 交叉注意力增强
        before_enhanced, _ = self.cross_attention(
            before_features.unsqueeze(1), 
            after_features.unsqueeze(1), 
            after_features.unsqueeze(1)
        )
        after_enhanced, _ = self.cross_attention(
            after_features.unsqueeze(1), 
            before_features.unsqueeze(1), 
            before_features.unsqueeze(1)
        )
        
        before_enhanced = before_enhanced.squeeze(1)
        after_enhanced = after_enhanced.squeeze(1)
        
        # 特征融合
        combined_features = torch.cat([before_enhanced, after_enhanced], dim=1)
        fused_features = self.feature_fusion(combined_features)
        relation_features = self.relation_layer(fused_features)
        
        # 共享表示
        shared_repr = self.shared_head(relation_features)
        
        # 预测
        start_bmi = self.start_bmi_head(shared_repr).squeeze()
        end_bmi = self.end_bmi_head(shared_repr).squeeze()
        bmi_change = self.bmi_change_head(shared_repr).squeeze()
        
        return {
            'start_bmi': start_bmi,
            'end_bmi': end_bmi,
            'bmi_change': bmi_change
        }
    
    def compute_loss(self, predictions, targets):
        """增强的动态权重损失"""
        start_loss = self.huber_loss(predictions['start_bmi'], targets['start_bmi'])
        end_loss = self.huber_loss(predictions['end_bmi'], targets['end_bmi'])
        change_loss = self.huber_loss(predictions['bmi_change'], targets['bmi_change'])
        
        # 动态权重调整
        losses = torch.stack([start_loss, end_loss, change_loss])
        weights = torch.exp(-self.log_vars)
        weighted_loss = torch.sum(weights * losses + self.log_vars)
        
        return {
            'total_loss': weighted_loss,
            'start_loss': start_loss,
            'end_loss': end_loss,
            'change_loss': change_loss,
            'task_weights': weights.detach()
        }


class ModelComparator:
    """模型对比评估器"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
    def create_test_data(self, batch_size=32, num_batches=10):
        """创建测试数据"""
        test_data = []
        
        for _ in range(num_batches):
            batch = {
                'before_image': torch.randn(batch_size, 3, 160, 160),
                'after_image': torch.randn(batch_size, 3, 160, 160),
                'start_bmi': torch.randn(batch_size) * 10 + 25,  # 15-35范围
                'end_bmi': torch.randn(batch_size) * 10 + 25,
                'bmi_change': torch.randn(batch_size) * 5  # -5到5的变化
            }
            test_data.append(batch)
        
        return test_data
    
    def evaluate_model(self, model, test_data, model_name):
        """评估单个模型"""
        model.eval()
        total_losses = []
        start_losses = []
        end_losses = []
        change_losses = []
        inference_times = []
        
        print(f"\n评估 {model_name}...")
        
        with torch.no_grad():
            for batch in test_data:
                # 移动数据到设备
                inputs = {
                    'before_image': batch['before_image'].to(self.device),
                    'after_image': batch['after_image'].to(self.device)
                }
                targets = {
                    'start_bmi': batch['start_bmi'].to(self.device),
                    'end_bmi': batch['end_bmi'].to(self.device),
                    'bmi_change': batch['bmi_change'].to(self.device)
                }
                
                # 测量推理时间
                start_time = time.time()
                predictions = model(inputs)
                inference_time = time.time() - start_time
                inference_times.append(inference_time)
                
                # 计算损失
                loss_info = model.compute_loss(predictions, targets)
                
                total_losses.append(loss_info['total_loss'].item())
                start_losses.append(loss_info['start_loss'].item())
                end_losses.append(loss_info['end_loss'].item())
                change_losses.append(loss_info['change_loss'].item())
        
        return {
            'total_loss': np.mean(total_losses),
            'start_loss': np.mean(start_losses),
            'end_loss': np.mean(end_losses),
            'change_loss': np.mean(change_losses),
            'inference_time': np.mean(inference_times),
            'std_total_loss': np.std(total_losses)
        }
    
    def compare_models(self):
        """对比两个模型"""
        print("=== Face-BMI模型优化前后对比评估 ===")
        
        # 创建模型
        original_model = OriginalBMIPredictor().to(self.device)
        enhanced_model = EnhancedBMIPredictor().to(self.device)
        
        # 模型复杂度对比
        original_params = sum(p.numel() for p in original_model.parameters())
        enhanced_params = sum(p.numel() for p in enhanced_model.parameters())
        
        print(f"\n模型复杂度对比:")
        print(f"原始模型参数数量: {original_params:,}")
        print(f"增强模型参数数量: {enhanced_params:,}")
        print(f"参数增长: {(enhanced_params - original_params) / original_params * 100:.1f}%")
        
        # 创建测试数据
        test_data = self.create_test_data()
        
        # 评估模型
        original_results = self.evaluate_model(original_model, test_data, "原始模型")
        enhanced_results = self.evaluate_model(enhanced_model, test_data, "增强模型")
        
        # 打印对比结果
        self.print_comparison(original_results, enhanced_results)
        
        # 绘制对比图
        self.plot_comparison(original_results, enhanced_results)
        
        return original_results, enhanced_results
    
    def print_comparison(self, original_results, enhanced_results):
        """打印对比结果"""
        print(f"\n=== 性能对比结果 ===")
        print(f"{'指标':<15} {'原始模型':<12} {'增强模型':<12} {'改善幅度':<10}")
        print("-" * 55)
        
        metrics = [
            ('总损失', 'total_loss'),
            ('起始BMI损失', 'start_loss'),
            ('结束BMI损失', 'end_loss'),
            ('BMI变化损失', 'change_loss'),
            ('推理时间(s)', 'inference_time')
        ]
        
        for name, key in metrics:
            original_val = original_results[key]
            enhanced_val = enhanced_results[key]
            
            if key == 'inference_time':
                improvement = (original_val - enhanced_val) / original_val * 100
                print(f"{name:<15} {original_val:<12.4f} {enhanced_val:<12.4f} {improvement:>+7.1f}%")
            else:
                improvement = (original_val - enhanced_val) / original_val * 100
                print(f"{name:<15} {original_val:<12.4f} {enhanced_val:<12.4f} {improvement:>+7.1f}%")
    
    def plot_comparison(self, original_results, enhanced_results):
        """绘制对比图表"""
        metrics = ['total_loss', 'start_loss', 'end_loss', 'change_loss']
        metric_names = ['总损失', '起始BMI损失', '结束BMI损失', 'BMI变化损失']
        
        original_values = [original_results[m] for m in metrics]
        enhanced_values = [enhanced_results[m] for m in metrics]
        
        x = np.arange(len(metrics))
        width = 0.35
        
        fig, ax = plt.subplots(figsize=(12, 6))
        
        bars1 = ax.bar(x - width/2, original_values, width, label='原始模型', alpha=0.8)
        bars2 = ax.bar(x + width/2, enhanced_values, width, label='增强模型', alpha=0.8)
        
        ax.set_xlabel('损失类型')
        ax.set_ylabel('损失值')
        ax.set_title('Face-BMI模型优化前后损失对比')
        ax.set_xticks(x)
        ax.set_xticklabels(metric_names)
        ax.legend()
        
        # 添加数值标签
        def autolabel(bars):
            for bar in bars:
                height = bar.get_height()
                ax.annotate(f'{height:.3f}',
                           xy=(bar.get_x() + bar.get_width() / 2, height),
                           xytext=(0, 3),
                           textcoords="offset points",
                           ha='center', va='bottom', fontsize=8)
        
        autolabel(bars1)
        autolabel(bars2)
        
        plt.tight_layout()
        plt.savefig('model_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"\n对比图表已保存为 model_comparison.png")


if __name__ == "__main__":
    comparator = ModelComparator()
    original_results, enhanced_results = comparator.compare_models()
    
    print(f"\n=== 总结 ===")
    print("增强模型的主要改进:")
    print("1. 使用交叉注意力机制增强特征融合")
    print("2. 采用动态任务权重调整")
    print("3. 使用Huber损失提高鲁棒性")
    print("4. 增加批归一化和更好的正则化")
    print("5. 多层次的特征表示学习")
