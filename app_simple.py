"""
Face-BMI Web应用 - 简化版本
不依赖复杂模型，用于快速部署测试
"""

from flask import Flask, request, jsonify, render_template, send_from_directory
from flask_cors import CORS
import numpy as np
from PIL import Image
import base64
import io
import os
import json
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 配置
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['RESULTS_FOLDER'] = 'results'

# 创建必要目录
os.makedirs('uploads', exist_ok=True)
os.makedirs('results', exist_ok=True)
os.makedirs('static/uploads', exist_ok=True)

def base64_to_image(base64_string):
    """将base64字符串转换为PIL图像"""
    try:
        # 移除data:image/jpeg;base64,前缀
        if ',' in base64_string:
            base64_string = base64_string.split(',')[1]
        
        # 解码base64
        image_data = base64.b64decode(base64_string)
        image = Image.open(io.BytesIO(image_data))
        return image
    except Exception as e:
        logger.error(f"Base64转图像失败: {e}")
        return None

def image_to_base64(image):
    """将PIL图像转换为base64字符串"""
    try:
        buffer = io.BytesIO()
        image.save(buffer, format='JPEG')
        img_str = base64.b64encode(buffer.getvalue()).decode()
        return f"data:image/jpeg;base64,{img_str}"
    except Exception as e:
        logger.error(f"图像转Base64失败: {e}")
        return None

@app.route('/')
def index():
    """主页面"""
    return render_template('index.html')

@app.route('/api/health')
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'message': 'Face-BMI服务器正常运行',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0',
        'models_loaded': {
            'bmi_predictor': False,  # 简化版本不加载模型
            'face_detector': False
        }
    })

@app.route('/api/predict/single', methods=['POST'])
def predict_single():
    """单张图片BMI预测"""
    try:
        data = request.get_json()
        
        if not data or 'image' not in data:
            return jsonify({'error': '缺少图像数据'}), 400
        
        # 转换图像
        image = base64_to_image(data['image'])
        if image is None:
            return jsonify({'error': '图像格式错误'}), 400
        
        # 模拟BMI预测结果
        bmi_value = 22.5 + np.random.normal(0, 2)  # 正常BMI范围内的随机值
        confidence = 0.85 + np.random.uniform(-0.1, 0.1)
        
        # 根据BMI值判断健康状态
        if bmi_value < 18.5:
            status = "偏瘦"
            color = "info"
        elif bmi_value < 24:
            status = "正常"
            color = "success"
        elif bmi_value < 28:
            status = "偏胖"
            color = "warning"
        else:
            status = "肥胖"
            color = "danger"
        
        return jsonify({
            'success': True,
            'prediction': {
                'bmi': round(float(bmi_value), 2),
                'confidence': round(float(confidence), 3),
                'status': status,
                'color': color
            },
            'face_detected': True,  # 模拟人脸检测成功
            'image_info': {
                'width': image.width,
                'height': image.height,
                'format': image.format or 'JPEG'
            },
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"单张预测失败: {e}")
        return jsonify({'error': f'预测失败: {str(e)}'}), 500

@app.route('/api/predict/paired', methods=['POST'])
def predict_paired():
    """配对图片BMI预测"""
    try:
        data = request.get_json()
        
        if not data or 'before_image' not in data or 'after_image' not in data:
            return jsonify({'error': '缺少before或after图像数据'}), 400
        
        # 转换图像
        before_image = base64_to_image(data['before_image'])
        after_image = base64_to_image(data['after_image'])
        
        if before_image is None or after_image is None:
            return jsonify({'error': '图像格式错误'}), 400
        
        # 模拟配对预测结果
        start_bmi = 25.0 + np.random.normal(0, 3)
        bmi_change = -np.random.uniform(1, 5)  # 模拟减重
        end_bmi = start_bmi + bmi_change
        confidence = 0.82 + np.random.uniform(-0.1, 0.1)
        
        # 计算减重效果
        weight_change_kg = bmi_change * 1.7 * 1.7  # 假设身高1.7m
        
        return jsonify({
            'success': True,
            'prediction': {
                'start_bmi': round(float(start_bmi), 2),
                'end_bmi': round(float(end_bmi), 2),
                'bmi_change': round(float(bmi_change), 2),
                'weight_change_kg': round(float(weight_change_kg), 2),
                'confidence': round(float(confidence), 3)
            },
            'faces_detected': {
                'before': True,
                'after': True
            },
            'images_info': {
                'before': {
                    'width': before_image.width,
                    'height': before_image.height,
                    'format': before_image.format or 'JPEG'
                },
                'after': {
                    'width': after_image.width,
                    'height': after_image.height,
                    'format': after_image.format or 'JPEG'
                }
            },
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"配对预测失败: {e}")
        return jsonify({'error': f'预测失败: {str(e)}'}), 500

@app.route('/api/upload', methods=['POST'])
def upload_file():
    """文件上传"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': '没有文件'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400
        
        if file:
            # 保存文件
            filename = f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{file.filename}"
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)
            
            # 转换为base64
            with open(filepath, 'rb') as f:
                image_data = f.read()
                base64_string = base64.b64encode(image_data).decode()
            
            return jsonify({
                'success': True,
                'filename': filename,
                'filepath': filepath,
                'base64': f"data:image/jpeg;base64,{base64_string}"
            })
    
    except Exception as e:
        logger.error(f"文件上传失败: {e}")
        return jsonify({'error': f'上传失败: {str(e)}'}), 500

@app.route('/api/stats')
def get_stats():
    """获取系统统计信息"""
    try:
        return jsonify({
            'success': True,
            'stats': {
                'total_predictions': np.random.randint(100, 1000),
                'today_predictions': np.random.randint(10, 50),
                'average_bmi': round(22.5 + np.random.normal(0, 1), 2),
                'server_uptime': '运行中',
                'server_time': datetime.now().isoformat()
            }
        })
    except Exception as e:
        return jsonify({'error': f'获取统计信息失败: {str(e)}'}), 500

@app.route('/static/<path:filename>')
def static_files(filename):
    """静态文件服务"""
    return send_from_directory('static', filename)

@app.errorhandler(413)
def too_large(e):
    return jsonify({'error': '文件太大，请上传小于16MB的文件'}), 413

@app.errorhandler(404)
def not_found(e):
    return jsonify({'error': '接口不存在'}), 404

@app.errorhandler(500)
def internal_error(e):
    return jsonify({'error': '服务器内部错误'}), 500

if __name__ == '__main__':
    logger.info("🚀 启动Face-BMI简化版服务器...")
    logger.info("📍 访问地址: http://localhost:5000")
    logger.info("🔧 这是简化版本，使用模拟数据进行演示")
    logger.info("-" * 50)
    
    # 启动应用
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )
