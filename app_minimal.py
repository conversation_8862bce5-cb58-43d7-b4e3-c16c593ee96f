"""
Face-BMI 最小化Web应用
用于快速测试部署
"""

from flask import Flask, jsonify, render_template_string
import json
from datetime import datetime

app = Flask(__name__)

# HTML模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Face-BMI 预测系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .card { border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
        .btn-primary { background: linear-gradient(45deg, #667eea, #764ba2); border: none; }
        .status-indicator { width: 12px; height: 12px; border-radius: 50%; display: inline-block; margin-right: 8px; }
        .status-online { background-color: #28a745; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white text-center">
                        <h2><i class="fas fa-user-md"></i> Face-BMI 预测系统</h2>
                        <p class="mb-0">基于人脸图像的BMI智能预测</p>
                    </div>
                    <div class="card-body">
                        <!-- 系统状态 -->
                        <div class="alert alert-success" role="alert">
                            <span class="status-indicator status-online"></span>
                            <strong>系统状态：</strong> 正常运行
                            <span class="float-end" id="server-time"></span>
                        </div>
                        
                        <!-- 功能说明 -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-body text-center">
                                        <h5 class="card-title">🖼️ 单张预测</h5>
                                        <p class="card-text">上传一张人脸照片，预测BMI值</p>
                                        <button class="btn btn-primary" onclick="testSinglePrediction()">测试预测</button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-body text-center">
                                        <h5 class="card-title">📊 配对预测</h5>
                                        <p class="card-text">对比前后照片，分析BMI变化</p>
                                        <button class="btn btn-primary" onclick="testPairedPrediction()">测试预测</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 结果显示区域 -->
                        <div id="result-area" class="mt-4" style="display: none;">
                            <div class="alert alert-info">
                                <h5>预测结果：</h5>
                                <div id="result-content"></div>
                            </div>
                        </div>
                        
                        <!-- API状态 -->
                        <div class="mt-4">
                            <h5>API接口状态：</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <button class="btn btn-outline-success btn-sm w-100" onclick="checkHealth()">健康检查</button>
                                </div>
                                <div class="col-md-4">
                                    <button class="btn btn-outline-info btn-sm w-100" onclick="getStats()">系统统计</button>
                                </div>
                                <div class="col-md-4">
                                    <button class="btn btn-outline-warning btn-sm w-100" onclick="checkModels()">模型状态</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 更新服务器时间
        function updateTime() {
            document.getElementById('server-time').textContent = new Date().toLocaleString('zh-CN');
        }
        setInterval(updateTime, 1000);
        updateTime();

        // 显示结果
        function showResult(title, content) {
            document.getElementById('result-content').innerHTML = `<strong>${title}</strong><br>${content}`;
            document.getElementById('result-area').style.display = 'block';
        }

        // 测试单张预测
        function testSinglePrediction() {
            showResult('单张预测测试', '模拟BMI预测：22.5 ± 2.0<br>置信度：85%<br>状态：正常体重');
        }

        // 测试配对预测
        function testPairedPrediction() {
            showResult('配对预测测试', '前：BMI 25.2<br>后：BMI 22.8<br>变化：-2.4<br>减重效果：良好');
        }

        // 健康检查
        async function checkHealth() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                showResult('健康检查', `状态：${data.status}<br>消息：${data.message}<br>时间：${data.timestamp}`);
            } catch (error) {
                showResult('健康检查', `错误：${error.message}`);
            }
        }

        // 获取统计
        async function getStats() {
            try {
                const response = await fetch('/api/stats');
                const data = await response.json();
                showResult('系统统计', `总预测次数：${data.stats.total_predictions}<br>今日预测：${data.stats.today_predictions}<br>平均BMI：${data.stats.average_bmi}`);
            } catch (error) {
                showResult('系统统计', `错误：${error.message}`);
            }
        }

        // 检查模型状态
        function checkModels() {
            showResult('模型状态', '✅ 系统正常运行<br>⚠️ 使用模拟数据<br>📝 完整模型需要额外配置');
        }
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """主页面"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/health')
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'message': 'Face-BMI服务器正常运行',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0-minimal'
    })

@app.route('/api/stats')
def get_stats():
    """获取系统统计信息"""
    import random
    return jsonify({
        'success': True,
        'stats': {
            'total_predictions': random.randint(100, 1000),
            'today_predictions': random.randint(10, 50),
            'average_bmi': round(22.5 + random.uniform(-2, 2), 2),
            'server_uptime': '运行中',
            'server_time': datetime.now().isoformat()
        }
    })

if __name__ == '__main__':
    print("🚀 启动Face-BMI最小化服务器...")
    print("📍 访问地址: http://localhost:5000")
    print("🔧 这是最小化版本，用于快速部署测试")
    print("-" * 50)
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )
