# Face-BMI 前端部署完整指南

## 📋 部署文件清单

我已经为您创建了完整的前端部署方案，包含以下文件：

### 🌐 Web应用核心文件
- **`app.py`** - Flask后端API服务
- **`templates/index.html`** - 前端HTML界面
- **`static/css/style.css`** - 样式文件
- **`static/js/app.js`** - JavaScript交互逻辑

### 🐳 容器化部署文件
- **`Dockerfile`** - Docker镜像构建文件
- **`docker-compose.yml`** - 多服务编排配置
- **`nginx.conf`** - Nginx反向代理配置
- **`requirements.txt`** - Python依赖包 (已更新)

### 🚀 部署脚本
- **`deploy.sh`** - 一键部署脚本
- **`前端部署说明.md`** - 本文档

## 🎯 功能特性

### Web界面功能
- ✅ **响应式设计** - 支持PC和移动端
- ✅ **拖拽上传** - 支持图片拖拽和点击上传
- ✅ **实时预览** - 图片上传后立即预览
- ✅ **双模式预测** - 单张图片和配对图片预测
- ✅ **结果可视化** - 美观的预测结果展示
- ✅ **状态监控** - 实时系统状态显示
- ✅ **错误处理** - 友好的错误提示

### 后端API功能
- ✅ **RESTful API** - 标准化API接口
- ✅ **文件上传** - 支持多种图片格式
- ✅ **模型集成** - 集成原有BMI预测模型
- ✅ **人脸检测** - 集成人脸检测功能
- ✅ **健康检查** - 系统状态监控
- ✅ **跨域支持** - CORS配置
- ✅ **错误处理** - 完善的异常处理

### 部署特性
- ✅ **Docker容器化** - 一键部署
- ✅ **Nginx代理** - 高性能Web服务器
- ✅ **Redis缓存** - 提升性能
- ✅ **监控系统** - Prometheus + Grafana
- ✅ **健康检查** - 自动故障检测
- ✅ **日志管理** - 完整的日志记录

## 🚀 快速部署

### 方法一：一键部署 (推荐)

```bash
# 1. 给部署脚本执行权限
chmod +x deploy.sh

# 2. 运行部署脚本
./deploy.sh

# 3. 等待部署完成，然后访问
# http://localhost
```

### 方法二：手动部署

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 启动Flask应用
python app.py

# 3. 访问应用
# http://localhost:5000
```

### 方法三：Docker部署

```bash
# 1. 构建镜像
docker-compose build

# 2. 启动服务
docker-compose up -d

# 3. 访问应用
# http://localhost
```

## 📁 项目结构

```
Face-BMI/
├── 原有文件 (保持不变)
│   ├── facenet_bmi_model.py
│   ├── bmi_predictor.py
│   ├── run_bmi_system.py
│   └── models/
│       ├── best_paired_bmi_model.pth
│       └── final_paired_bmi_model.pth
├── 新增前端文件
│   ├── app.py                    # Flask后端服务
│   ├── templates/
│   │   └── index.html           # 前端界面
│   ├── static/
│   │   ├── css/
│   │   │   └── style.css        # 样式文件
│   │   └── js/
│   │       └── app.js           # JavaScript逻辑
│   ├── uploads/                 # 上传文件目录
│   ├── results/                 # 结果文件目录
│   └── logs/                    # 日志目录
├── 部署配置文件
│   ├── Dockerfile               # Docker镜像配置
│   ├── docker-compose.yml       # 服务编排配置
│   ├── nginx.conf               # Nginx配置
│   ├── requirements.txt         # Python依赖 (已更新)
│   └── deploy.sh                # 部署脚本
└── 文档
    └── 前端部署说明.md           # 本文档
```

## 🔧 配置说明

### 环境要求
- **Python**: 3.9+
- **Docker**: 20.0+
- **Docker Compose**: 2.0+
- **内存**: 最少4GB
- **存储**: 最少10GB

### 端口配置
- **80**: Nginx HTTP服务
- **443**: Nginx HTTPS服务 (可选)
- **5000**: Flask应用服务
- **6379**: Redis缓存服务
- **9090**: Prometheus监控 (可选)
- **3000**: Grafana仪表板 (可选)

### 环境变量
```bash
# Flask配置
FLASK_ENV=production
FLASK_APP=app.py

# 文件上传配置
MAX_CONTENT_LENGTH=16MB
UPLOAD_FOLDER=uploads
RESULTS_FOLDER=results
```

## 🌐 API接口文档

### 健康检查
```http
GET /api/health
```

### 单张图片预测
```http
POST /api/predict/single
Content-Type: application/json

{
    "image": "data:image/jpeg;base64,..."
}
```

### 配对图片预测
```http
POST /api/predict/paired
Content-Type: application/json

{
    "before_image": "data:image/jpeg;base64,...",
    "after_image": "data:image/jpeg;base64,..."
}
```

### 文件上传
```http
POST /api/upload
Content-Type: multipart/form-data

file: [图片文件]
```

### 系统统计
```http
GET /api/stats
```

## 🎨 界面预览

### 主界面特性
- **现代化设计** - Bootstrap 5 + 自定义CSS
- **双模式切换** - 标签页切换单张/配对预测
- **实时状态** - 系统状态实时显示
- **拖拽上传** - 支持拖拽和点击上传
- **结果展示** - 美观的预测结果卡片
- **响应式布局** - 适配各种屏幕尺寸

### 交互功能
- **图片预览** - 上传后立即预览
- **进度提示** - 预测过程加载动画
- **错误处理** - 友好的错误提示
- **结果动画** - 平滑的结果展示动画

## 🔍 监控和日志

### 系统监控
- **健康检查**: 自动检测服务状态
- **性能监控**: Prometheus + Grafana
- **日志收集**: 结构化日志记录
- **错误追踪**: 详细的错误日志

### 日志查看
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f face-bmi-app

# 查看Nginx日志
docker-compose logs -f nginx
```

## 🛠️ 管理命令

### 部署管理
```bash
# 部署系统
./deploy.sh deploy

# 停止服务
./deploy.sh stop

# 重启服务
./deploy.sh restart

# 查看日志
./deploy.sh logs

# 健康检查
./deploy.sh health

# 清理资源
./deploy.sh clean
```

### Docker管理
```bash
# 查看运行状态
docker-compose ps

# 重启特定服务
docker-compose restart face-bmi-app

# 查看资源使用
docker stats

# 进入容器
docker-compose exec face-bmi-app bash
```

## 🔒 安全配置

### 生产环境建议
1. **HTTPS配置** - 启用SSL证书
2. **防火墙设置** - 限制端口访问
3. **访问控制** - 配置IP白名单
4. **文件上传限制** - 严格的文件类型检查
5. **日志审计** - 记录所有访问日志

### SSL证书配置
```bash
# 将证书文件放入ssl目录
mkdir -p ssl
cp your-cert.pem ssl/cert.pem
cp your-key.pem ssl/key.pem

# 取消nginx.conf中HTTPS配置的注释
# 重启服务
docker-compose restart nginx
```

## 🚨 故障排除

### 常见问题

1. **模型文件缺失**
   ```bash
   # 确保模型文件存在
   ls -la models/
   # 如果缺失，请从训练环境复制
   ```

2. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :80
   # 修改docker-compose.yml中的端口映射
   ```

3. **内存不足**
   ```bash
   # 检查内存使用
   free -h
   # 减少worker数量或增加内存
   ```

4. **权限问题**
   ```bash
   # 修复文件权限
   sudo chown -R $USER:$USER .
   chmod +x deploy.sh
   ```

### 调试模式
```bash
# 开发模式启动
export FLASK_ENV=development
python app.py

# 查看详细日志
docker-compose logs -f --tail=100 face-bmi-app
```

## 📈 性能优化

### 建议配置
- **CPU**: 4核心以上
- **内存**: 8GB以上
- **存储**: SSD硬盘
- **网络**: 100Mbps以上

### 优化建议
1. **启用Redis缓存** - 缓存预测结果
2. **配置CDN** - 加速静态资源
3. **负载均衡** - 多实例部署
4. **数据库优化** - 使用PostgreSQL存储结果
5. **图片压缩** - 自动压缩上传图片

## 🎉 部署完成

部署完成后，您将拥有：

✅ **完整的Web界面** - 现代化的BMI预测系统  
✅ **高性能后端** - Flask + Gunicorn + Nginx  
✅ **容器化部署** - Docker + Docker Compose  
✅ **监控系统** - 实时状态监控  
✅ **完全兼容** - 不影响原有系统  

访问 **http://localhost** 开始使用您的Face-BMI Web应用！

---

**注意**: 这套前端系统完全独立于原有系统，可以安全部署和测试，不会影响您现有的Face-BMI项目功能。
