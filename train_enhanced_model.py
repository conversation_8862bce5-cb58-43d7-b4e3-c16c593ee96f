"""
Enhanced BMI Model Training Script
增强BMI模型训练脚本
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import json
import os
from PIL import Image
import torchvision.transforms as transforms
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm
import argparse

from enhanced_bmi_model import EnhancedPairedBMIPredictor, BMIDataCleaner


class EnhancedPairedFaceDataset(Dataset):
    """增强的配对面部数据集"""
    
    def __init__(self, data, root_dir, transform=None, mode='paired'):
        self.data = data
        self.root_dir = root_dir
        self.transform = transform
        self.mode = mode
        
        # BMI感知的数据增强
        self.bmi_aware_transform = transforms.Compose([
            transforms.RandomHorizontalFlip(p=0.5),
            transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.1),
            transforms.RandomRotation(degrees=3),
            transforms.RandomResizedCrop(160, scale=(0.95, 1.0))
        ])
        
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        
        # 加载图片
        before_path = os.path.join(self.root_dir, item['before_image_path'])
        after_path = os.path.join(self.root_dir, item['after_image_path'])
        
        try:
            before_image = Image.open(before_path).convert('RGB')
            after_image = Image.open(after_path).convert('RGB')
        except Exception as e:
            print(f"Error loading images: {e}")
            # 返回默认图片
            before_image = Image.new('RGB', (160, 160), color='gray')
            after_image = Image.new('RGB', (160, 160), color='gray')
        
        # 应用变换
        if self.transform:
            before_image = self.transform(before_image)
            after_image = self.transform(after_image)
            
            # BMI感知增强（对高BMI样本应用更强的增强）
            start_bmi = item['start_bmi']
            if start_bmi > 35 and np.random.random() < 0.3:
                before_image = self.bmi_aware_transform(transforms.ToPILImage()(before_image))
                after_image = self.bmi_aware_transform(transforms.ToPILImage()(after_image))
                before_image = transforms.ToTensor()(before_image)
                after_image = transforms.ToTensor()(after_image)
        
        # 准备标签
        start_bmi = float(item['start_bmi'])
        end_bmi = float(item['end_bmi'])
        bmi_change = end_bmi - start_bmi
        
        return {
            'before_image': before_image,
            'after_image': after_image,
            'start_bmi': start_bmi,
            'end_bmi': end_bmi,
            'bmi_change': bmi_change
        }


class EnhancedTrainer:
    """增强模型训练器"""
    
    def __init__(self, model, train_loader, val_loader, device='cuda'):
        self.model = model.to(device)
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.device = device
        
        # 优化器设置
        self.optimizer = optim.AdamW(
            model.parameters(), 
            lr=1e-4, 
            weight_decay=1e-5,
            betas=(0.9, 0.999)
        )
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, 
            mode='min', 
            factor=0.5, 
            patience=5,
            verbose=True
        )
        
        # 训练历史
        self.train_history = {
            'total_loss': [], 'start_loss': [], 'end_loss': [], 'change_loss': [],
            'val_total_loss': [], 'val_start_loss': [], 'val_end_loss': [], 'val_change_loss': [],
            'task_weights': []
        }
        
    def train_epoch(self):
        """训练一个epoch"""
        self.model.train()
        epoch_losses = {'total': 0, 'start': 0, 'end': 0, 'change': 0}
        epoch_weights = []
        
        pbar = tqdm(self.train_loader, desc='Training')
        for batch_idx, batch in enumerate(pbar):
            # 移动数据到设备
            inputs = {
                'before_image': batch['before_image'].to(self.device),
                'after_image': batch['after_image'].to(self.device)
            }
            targets = {
                'start_bmi': batch['start_bmi'].to(self.device),
                'end_bmi': batch['end_bmi'].to(self.device),
                'bmi_change': batch['bmi_change'].to(self.device)
            }
            
            # 前向传播
            self.optimizer.zero_grad()
            predictions = self.model(inputs)
            loss_info = self.model.compute_loss(predictions, targets)
            
            # 反向传播
            loss_info['total_loss'].backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            # 记录损失
            epoch_losses['total'] += loss_info['total_loss'].item()
            epoch_losses['start'] += loss_info['start_loss'].item()
            epoch_losses['end'] += loss_info['end_loss'].item()
            epoch_losses['change'] += loss_info['change_loss'].item()
            epoch_weights.append(loss_info['task_weights'].detach().cpu().numpy())
            
            # 更新进度条
            pbar.set_postfix({
                'Loss': f"{loss_info['total_loss'].item():.4f}",
                'LR': f"{self.optimizer.param_groups[0]['lr']:.6f}"
            })
        
        # 计算平均损失
        num_batches = len(self.train_loader)
        for key in epoch_losses:
            epoch_losses[key] /= num_batches
        
        avg_weights = np.mean(epoch_weights, axis=0)
        
        return epoch_losses, avg_weights
    
    def validate(self):
        """验证模型"""
        self.model.eval()
        val_losses = {'total': 0, 'start': 0, 'end': 0, 'change': 0}
        
        with torch.no_grad():
            for batch in tqdm(self.val_loader, desc='Validation'):
                inputs = {
                    'before_image': batch['before_image'].to(self.device),
                    'after_image': batch['after_image'].to(self.device)
                }
                targets = {
                    'start_bmi': batch['start_bmi'].to(self.device),
                    'end_bmi': batch['end_bmi'].to(self.device),
                    'bmi_change': batch['bmi_change'].to(self.device)
                }
                
                predictions = self.model(inputs)
                loss_info = self.model.compute_loss(predictions, targets)
                
                val_losses['total'] += loss_info['total_loss'].item()
                val_losses['start'] += loss_info['start_loss'].item()
                val_losses['end'] += loss_info['end_loss'].item()
                val_losses['change'] += loss_info['change_loss'].item()
        
        # 计算平均损失
        num_batches = len(self.val_loader)
        for key in val_losses:
            val_losses[key] /= num_batches
        
        return val_losses
    
    def train(self, num_epochs=50, save_path='models/enhanced_bmi_model.pth'):
        """完整训练流程"""
        best_val_loss = float('inf')
        patience_counter = 0
        max_patience = 10
        
        print(f"开始训练增强BMI模型，共 {num_epochs} 个epoch")
        
        for epoch in range(num_epochs):
            print(f"\nEpoch {epoch+1}/{num_epochs}")
            
            # 训练
            train_losses, task_weights = self.train_epoch()
            
            # 验证
            val_losses = self.validate()
            
            # 更新学习率
            self.scheduler.step(val_losses['total'])
            
            # 记录历史
            self.train_history['total_loss'].append(train_losses['total'])
            self.train_history['start_loss'].append(train_losses['start'])
            self.train_history['end_loss'].append(train_losses['end'])
            self.train_history['change_loss'].append(train_losses['change'])
            self.train_history['val_total_loss'].append(val_losses['total'])
            self.train_history['val_start_loss'].append(val_losses['start'])
            self.train_history['val_end_loss'].append(val_losses['end'])
            self.train_history['val_change_loss'].append(val_losses['change'])
            self.train_history['task_weights'].append(task_weights)
            
            # 打印结果
            print(f"训练损失: {train_losses['total']:.4f} | 验证损失: {val_losses['total']:.4f}")
            print(f"任务权重: Start={task_weights[0]:.3f}, End={task_weights[1]:.3f}, Change={task_weights[2]:.3f}")
            
            # 保存最佳模型
            if val_losses['total'] < best_val_loss:
                best_val_loss = val_losses['total']
                patience_counter = 0
                
                # 保存模型
                os.makedirs(os.path.dirname(save_path), exist_ok=True)
                torch.save({
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'epoch': epoch,
                    'best_val_loss': best_val_loss,
                    'train_history': self.train_history
                }, save_path)
                print(f"保存最佳模型到 {save_path}")
            else:
                patience_counter += 1
            
            # 早停
            if patience_counter >= max_patience:
                print(f"验证损失连续 {max_patience} 个epoch未改善，提前停止训练")
                break
        
        print("训练完成！")
        return self.train_history
    
    def plot_training_curves(self, save_path='enhanced_training_curves.png'):
        """绘制训练曲线"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        epochs = range(1, len(self.train_history['total_loss']) + 1)
        
        # 总损失
        ax1.plot(epochs, self.train_history['total_loss'], 'b-', label='Train')
        ax1.plot(epochs, self.train_history['val_total_loss'], 'r-', label='Validation')
        ax1.set_title('Total Loss')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.legend()
        ax1.grid(True)
        
        # 各任务损失
        ax2.plot(epochs, self.train_history['start_loss'], 'g-', label='Start BMI')
        ax2.plot(epochs, self.train_history['end_loss'], 'b-', label='End BMI')
        ax2.plot(epochs, self.train_history['change_loss'], 'r-', label='BMI Change')
        ax2.set_title('Task-specific Losses (Training)')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Loss')
        ax2.legend()
        ax2.grid(True)
        
        # 验证损失分解
        ax3.plot(epochs, self.train_history['val_start_loss'], 'g-', label='Start BMI')
        ax3.plot(epochs, self.train_history['val_end_loss'], 'b-', label='End BMI')
        ax3.plot(epochs, self.train_history['val_change_loss'], 'r-', label='BMI Change')
        ax3.set_title('Task-specific Losses (Validation)')
        ax3.set_xlabel('Epoch')
        ax3.set_ylabel('Loss')
        ax3.legend()
        ax3.grid(True)
        
        # 任务权重变化
        task_weights = np.array(self.train_history['task_weights'])
        ax4.plot(epochs, task_weights[:, 0], 'g-', label='Start BMI Weight')
        ax4.plot(epochs, task_weights[:, 1], 'b-', label='End BMI Weight')
        ax4.plot(epochs, task_weights[:, 2], 'r-', label='BMI Change Weight')
        ax4.set_title('Dynamic Task Weights')
        ax4.set_xlabel('Epoch')
        ax4.set_ylabel('Weight')
        ax4.legend()
        ax4.grid(True)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"训练曲线已保存到 {save_path}")


def main():
    parser = argparse.ArgumentParser(description='Train Enhanced BMI Model')
    parser.add_argument('--data_path', type=str, default='summary.json', help='Path to data file')
    parser.add_argument('--root_dir', type=str, default='.', help='Root directory for images')
    parser.add_argument('--batch_size', type=int, default=16, help='Batch size')
    parser.add_argument('--epochs', type=int, default=50, help='Number of epochs')
    parser.add_argument('--device', type=str, default='cuda', help='Device to use')
    
    args = parser.parse_args()
    
    # 设置设备
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载和清洗数据
    print("加载数据...")
    with open(args.data_path, 'r') as f:
        data = json.load(f)
    
    cleaner = BMIDataCleaner()
    cleaned_data = cleaner.clean_bmi_data(data)
    cleaner.analyze_bmi_distribution(cleaned_data)
    
    # 数据分割
    train_size = int(0.8 * len(cleaned_data))
    val_size = len(cleaned_data) - train_size
    
    train_data = cleaned_data[:train_size]
    val_data = cleaned_data[train_size:]
    
    print(f"训练集: {len(train_data)} 样本")
    print(f"验证集: {len(val_data)} 样本")
    
    # 数据变换
    transform = transforms.Compose([
        transforms.Resize((160, 160)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # 创建数据集和数据加载器
    train_dataset = EnhancedPairedFaceDataset(train_data, args.root_dir, transform)
    val_dataset = EnhancedPairedFaceDataset(val_data, args.root_dir, transform)
    
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True, num_workers=4)
    val_loader = DataLoader(val_dataset, batch_size=args.batch_size, shuffle=False, num_workers=4)
    
    # 创建模型
    print("创建增强BMI模型...")
    model = EnhancedPairedBMIPredictor(pretrained='vggface2', dropout_rate=0.3)
    
    # 创建训练器并开始训练
    trainer = EnhancedTrainer(model, train_loader, val_loader, device)
    history = trainer.train(num_epochs=args.epochs)
    
    # 绘制训练曲线
    trainer.plot_training_curves()


if __name__ == "__main__":
    main()
