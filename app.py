"""
Face-BMI Web应用主程序
Flask后端API服务
"""

from flask import Flask, request, jsonify, render_template, send_from_directory
from flask_cors import CORS
import torch
import cv2
import numpy as np
from PIL import Image
import base64
import io
import os
import json
from datetime import datetime
import logging

# 导入模型 - 延迟导入避免启动时错误
# from bmi_predictor import PairedBMIInference
# from face_detector import FaceDetector

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 配置
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['RESULTS_FOLDER'] = 'results'

# 确保文件夹存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['RESULTS_FOLDER'], exist_ok=True)
os.makedirs('static', exist_ok=True)

# 全局变量
bmi_predictor = None
face_detector = None

def initialize_models():
    """初始化模型"""
    global bmi_predictor, face_detector

    try:
        # 延迟导入模型模块
        from bmi_predictor import PairedBMIInference
        from face_detector import FaceDetector

        # 初始化BMI预测器
        model_path = "models/best_paired_bmi_model.pth"
        if os.path.exists(model_path):
            bmi_predictor = PairedBMIInference(model_path, use_pairing=True)
            logger.info("BMI预测模型加载成功")
        else:
            logger.warning(f"模型文件不存在: {model_path}")

        # 初始化人脸检测器
        face_detector = FaceDetector()
        logger.info("人脸检测器初始化成功")

    except Exception as e:
        logger.error(f"模型初始化失败: {e}")
        logger.info("将以测试模式运行，预测功能不可用")

def base64_to_image(base64_string):
    """将base64字符串转换为PIL图像"""
    try:
        # 移除data:image/jpeg;base64,前缀
        if ',' in base64_string:
            base64_string = base64_string.split(',')[1]
        
        # 解码base64
        image_data = base64.b64decode(base64_string)
        image = Image.open(io.BytesIO(image_data))
        return image
    except Exception as e:
        logger.error(f"Base64转图像失败: {e}")
        return None

def image_to_base64(image):
    """将PIL图像转换为base64字符串"""
    try:
        buffer = io.BytesIO()
        image.save(buffer, format='JPEG')
        img_str = base64.b64encode(buffer.getvalue()).decode()
        return f"data:image/jpeg;base64,{img_str}"
    except Exception as e:
        logger.error(f"图像转Base64失败: {e}")
        return None

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/health')
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'models_loaded': {
            'bmi_predictor': bmi_predictor is not None,
            'face_detector': face_detector is not None
        }
    })

@app.route('/api/predict/single', methods=['POST'])
def predict_single():
    """单张图片BMI预测"""
    try:
        data = request.get_json()
        
        if not data or 'image' not in data:
            return jsonify({'error': '缺少图像数据'}), 400
        
        # 转换图像
        image = base64_to_image(data['image'])
        if image is None:
            return jsonify({'error': '图像格式错误'}), 400
        
        # 检查模型是否加载
        if bmi_predictor is None:
            # 返回模拟数据用于测试
            result = {
                'bmi': 22.5 + np.random.normal(0, 2),  # 模拟BMI值
                'confidence': 0.85
            }
        else:
            # 进行预测
            result = bmi_predictor.predict_from_image_pil(image)
        
        # 人脸检测（可选）
        face_detected = False
        if face_detector:
            try:
                # 转换为OpenCV格式
                cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
                faces = face_detector.detect_faces(cv_image)
                face_detected = len(faces) > 0
            except:
                pass
        
        return jsonify({
            'success': True,
            'prediction': {
                'bmi': float(result),
                'bmi_category': get_bmi_category(result),
                'face_detected': face_detected
            },
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"单张预测失败: {e}")
        return jsonify({'error': f'预测失败: {str(e)}'}), 500

@app.route('/api/predict/paired', methods=['POST'])
def predict_paired():
    """配对图片BMI预测"""
    try:
        data = request.get_json()
        
        if not data or 'before_image' not in data or 'after_image' not in data:
            return jsonify({'error': '缺少before或after图像数据'}), 400
        
        # 转换图像
        before_image = base64_to_image(data['before_image'])
        after_image = base64_to_image(data['after_image'])
        
        if before_image is None or after_image is None:
            return jsonify({'error': '图像格式错误'}), 400
        
        # 检查模型是否加载
        if bmi_predictor is None:
            # 返回模拟数据用于测试
            start_bmi = 25.0 + np.random.normal(0, 3)
            end_bmi = start_bmi - np.random.uniform(1, 5)  # 模拟减重
            result = {
                'start_bmi': start_bmi,
                'end_bmi': end_bmi,
                'bmi_change': end_bmi - start_bmi,
                'confidence': 0.82
            }
        else:
            # 进行配对预测
            result = bmi_predictor.predict_from_pair_pil(before_image, after_image)
        
        # 人脸检测
        faces_detected = {'before': False, 'after': False}
        if face_detector:
            try:
                # 检测before图像中的人脸
                cv_before = cv2.cvtColor(np.array(before_image), cv2.COLOR_RGB2BGR)
                faces_before = face_detector.detect_faces(cv_before)
                faces_detected['before'] = len(faces_before) > 0
                
                # 检测after图像中的人脸
                cv_after = cv2.cvtColor(np.array(after_image), cv2.COLOR_RGB2BGR)
                faces_after = face_detector.detect_faces(cv_after)
                faces_detected['after'] = len(faces_after) > 0
            except:
                pass
        
        return jsonify({
            'success': True,
            'prediction': {
                'start_bmi': float(result['start_bmi']),
                'end_bmi': float(result['end_bmi']),
                'bmi_change': float(result['bmi_change']),
                'start_category': get_bmi_category(result['start_bmi']),
                'end_category': get_bmi_category(result['end_bmi']),
                'faces_detected': faces_detected
            },
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"配对预测失败: {e}")
        return jsonify({'error': f'预测失败: {str(e)}'}), 500

@app.route('/api/upload', methods=['POST'])
def upload_file():
    """文件上传接口"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': '没有文件'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400
        
        if file and allowed_file(file.filename):
            # 保存文件
            filename = secure_filename(file.filename)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{timestamp}_{filename}"
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)
            
            # 转换为base64返回
            with open(filepath, 'rb') as f:
                image_data = base64.b64encode(f.read()).decode()
            
            return jsonify({
                'success': True,
                'filename': filename,
                'image_data': f"data:image/jpeg;base64,{image_data}"
            })
        
        return jsonify({'error': '不支持的文件格式'}), 400
        
    except Exception as e:
        logger.error(f"文件上传失败: {e}")
        return jsonify({'error': f'上传失败: {str(e)}'}), 500

def allowed_file(filename):
    """检查文件格式是否允许"""
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp'}
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def secure_filename(filename):
    """安全的文件名"""
    import re
    filename = re.sub(r'[^\w\s-]', '', filename).strip()
    return re.sub(r'[-\s]+', '-', filename)

def get_bmi_category(bmi):
    """根据BMI值获取分类"""
    if bmi < 18.5:
        return "偏瘦"
    elif bmi < 24:
        return "正常"
    elif bmi < 28:
        return "偏胖"
    else:
        return "肥胖"

@app.route('/api/stats')
def get_stats():
    """获取系统统计信息"""
    try:
        # 统计上传文件数量
        upload_count = len([f for f in os.listdir(app.config['UPLOAD_FOLDER']) 
                           if os.path.isfile(os.path.join(app.config['UPLOAD_FOLDER'], f))])
        
        return jsonify({
            'success': True,
            'stats': {
                'total_uploads': upload_count,
                'models_status': {
                    'bmi_predictor': 'loaded' if bmi_predictor else 'not_loaded',
                    'face_detector': 'loaded' if face_detector else 'not_loaded'
                },
                'server_time': datetime.now().isoformat()
            }
        })
    except Exception as e:
        return jsonify({'error': f'获取统计信息失败: {str(e)}'}), 500

@app.errorhandler(413)
def too_large(e):
    return jsonify({'error': '文件太大，请上传小于16MB的文件'}), 413

@app.errorhandler(404)
def not_found(e):
    return jsonify({'error': '接口不存在'}), 404

@app.errorhandler(500)
def internal_error(e):
    return jsonify({'error': '服务器内部错误'}), 500

if __name__ == '__main__':
    # 初始化模型
    initialize_models()
    
    # 启动应用
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )
