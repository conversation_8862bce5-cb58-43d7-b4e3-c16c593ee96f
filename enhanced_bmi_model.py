"""
Enhanced BMI Prediction Model with Optimizations
优化后的BMI预测模型实现
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from facenet_pytorch import InceptionResnetV1
import math


class RobustBMILoss(nn.Module):
    """鲁棒的BMI损失函数，结合Huber Loss和Focal Loss"""
    
    def __init__(self, delta=1.0, alpha=0.5, reduction='mean'):
        super().__init__()
        self.delta = delta
        self.alpha = alpha
        self.reduction = reduction
    
    def forward(self, pred, target):
        # Huber Loss for robustness to outliers
        diff = torch.abs(pred - target)
        huber_loss = torch.where(
            diff < self.delta,
            0.5 * diff ** 2,
            self.delta * (diff - 0.5 * self.delta)
        )
        
        # Focal weight for hard samples
        focal_weight = torch.pow(diff / 10.0, self.alpha)
        
        loss = focal_weight * huber_loss
        
        if self.reduction == 'mean':
            return torch.mean(loss)
        elif self.reduction == 'sum':
            return torch.sum(loss)
        else:
            return loss


class DynamicTaskWeighting(nn.Module):
    """基于不确定性的动态任务权重调整"""
    
    def __init__(self, num_tasks=3):
        super().__init__()
        self.num_tasks = num_tasks
        self.log_vars = nn.Parameter(torch.zeros(num_tasks))
    
    def forward(self, losses):
        """
        losses: list of task losses [start_loss, end_loss, change_loss]
        """
        losses = torch.stack(losses)
        weights = torch.exp(-self.log_vars)
        weighted_loss = torch.sum(weights * losses + self.log_vars)
        return weighted_loss, weights


class MultiScaleConv(nn.Module):
    """多尺度卷积模块"""
    
    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.conv1 = nn.Conv2d(in_channels, out_channels//3, 3, padding=1)
        self.conv2 = nn.Conv2d(in_channels, out_channels//3, 5, padding=2)
        self.conv3 = nn.Conv2d(in_channels, out_channels//3, 7, padding=3)
        self.bn = nn.BatchNorm2d(out_channels)
        self.relu = nn.ReLU(inplace=True)
        
    def forward(self, x):
        conv1_out = self.conv1(x)
        conv2_out = self.conv2(x)
        conv3_out = self.conv3(x)
        
        out = torch.cat([conv1_out, conv2_out, conv3_out], dim=1)
        out = self.bn(out)
        out = self.relu(out)
        
        return out


class CrossAttentionFusion(nn.Module):
    """交叉注意力融合模块"""
    
    def __init__(self, feature_dim=512, num_heads=8):
        super().__init__()
        self.feature_dim = feature_dim
        self.num_heads = num_heads
        self.head_dim = feature_dim // num_heads
        
        assert feature_dim % num_heads == 0, "feature_dim must be divisible by num_heads"
        
        self.query_proj = nn.Linear(feature_dim, feature_dim)
        self.key_proj = nn.Linear(feature_dim, feature_dim)
        self.value_proj = nn.Linear(feature_dim, feature_dim)
        self.out_proj = nn.Linear(feature_dim, feature_dim)
        
        self.scale = self.head_dim ** -0.5
        self.dropout = nn.Dropout(0.1)
        
    def forward(self, query_features, key_value_features):
        batch_size = query_features.size(0)
        
        # Project to Q, K, V
        Q = self.query_proj(query_features)
        K = self.key_proj(key_value_features)
        V = self.value_proj(key_value_features)
        
        # Reshape for multi-head attention
        Q = Q.view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        K = K.view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        V = V.view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        
        # Attention computation
        attention_scores = torch.matmul(Q, K.transpose(-2, -1)) * self.scale
        attention_weights = F.softmax(attention_scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        attended_values = torch.matmul(attention_weights, V)
        
        # Reshape and project
        attended_values = attended_values.transpose(1, 2).contiguous().view(
            batch_size, -1, self.feature_dim
        )
        
        output = self.out_proj(attended_values)
        
        # Residual connection
        return query_features + output


class EnhancedFaceEncoder(nn.Module):
    """增强的面部特征编码器"""

    def __init__(self, pretrained='vggface2', dropout_rate=0.3):
        super().__init__()

        # Base FaceNet model
        self.facenet = InceptionResnetV1(pretrained=pretrained)

        # Freeze early layers, keep last layers trainable
        for param in list(self.facenet.parameters())[:-20]:
            param.requires_grad = False

        # Get the feature dimension from FaceNet (512)
        facenet_feature_dim = 512

        # Multi-scale feature enhancement (applied after FaceNet features)
        self.multiscale_enhancement = nn.Sequential(
            nn.Linear(facenet_feature_dim, 256),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(256, 512),
            nn.BatchNorm1d(512),
            nn.ReLU()
        )

        # Feature projection
        self.feature_proj = nn.Sequential(
            nn.Linear(512, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(dropout_rate)
        )

    def forward(self, x):
        # Use FaceNet's complete forward pass to get 512-dim features
        base_features = self.facenet(x)

        # Apply multi-scale enhancement
        enhanced_features = self.multiscale_enhancement(base_features)

        # Final projection
        output_features = self.feature_proj(enhanced_features)

        return output_features


class EnhancedPairedBMIPredictor(nn.Module):
    """增强的配对BMI预测模型"""
    
    def __init__(self, pretrained='vggface2', dropout_rate=0.3):
        super().__init__()
        
        # Enhanced feature extractor
        self.feature_extractor = EnhancedFaceEncoder(pretrained, dropout_rate)
        
        # Cross-attention fusion
        self.cross_attention = CrossAttentionFusion(512, num_heads=8)
        
        # Feature fusion with residual connections
        self.feature_fusion = nn.Sequential(
            nn.Linear(1024, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(dropout_rate)
        )
        
        # Relation modeling
        self.relation_layer = nn.Sequential(
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(dropout_rate)
        )
        
        # Shared representation
        self.shared_head = nn.Sequential(
            nn.Linear(128, 64),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(dropout_rate)
        )
        
        # Task-specific heads
        self.start_bmi_head = nn.Sequential(
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 1)
        )
        
        self.end_bmi_head = nn.Sequential(
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 1)
        )
        
        self.bmi_change_head = nn.Sequential(
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 1)
        )
        
        # Dynamic task weighting
        self.task_weighting = DynamicTaskWeighting(3)
        
        # Loss function
        self.criterion = RobustBMILoss(delta=1.5, alpha=0.3)
        
    def forward(self, x):
        before_img = x['before_image']
        after_img = x['after_image']
        
        # Extract features
        before_features = self.feature_extractor(before_img)
        after_features = self.feature_extractor(after_img)
        
        # Cross-attention enhancement
        enhanced_before = self.cross_attention(before_features.unsqueeze(1), after_features.unsqueeze(1)).squeeze(1)
        enhanced_after = self.cross_attention(after_features.unsqueeze(1), before_features.unsqueeze(1)).squeeze(1)
        
        # Feature fusion
        combined_features = torch.cat([enhanced_before, enhanced_after], dim=1)
        fused_features = self.feature_fusion(combined_features)
        relation_features = self.relation_layer(fused_features)
        
        # Shared representation
        shared_repr = self.shared_head(relation_features)
        
        # Multi-task predictions
        start_bmi = self.start_bmi_head(shared_repr).squeeze()
        end_bmi = self.end_bmi_head(shared_repr).squeeze()
        bmi_change = self.bmi_change_head(shared_repr).squeeze()
        
        return {
            'start_bmi': start_bmi,
            'end_bmi': end_bmi,
            'bmi_change': bmi_change
        }
    
    def compute_loss(self, predictions, targets):
        """计算多任务损失"""
        start_loss = self.criterion(predictions['start_bmi'], targets['start_bmi'])
        end_loss = self.criterion(predictions['end_bmi'], targets['end_bmi'])
        change_loss = self.criterion(predictions['bmi_change'], targets['bmi_change'])
        
        # Dynamic weighting
        total_loss, weights = self.task_weighting([start_loss, end_loss, change_loss])
        
        return {
            'total_loss': total_loss,
            'start_loss': start_loss,
            'end_loss': end_loss,
            'change_loss': change_loss,
            'task_weights': weights
        }


def create_enhanced_model(pretrained='vggface2', dropout_rate=0.3):
    """创建增强的BMI预测模型"""
    model = EnhancedPairedBMIPredictor(pretrained=pretrained, dropout_rate=dropout_rate)
    return model


class BMIDataCleaner:
    """BMI数据清洗工具"""

    @staticmethod
    def clean_bmi_data(data, bmi_min=10, bmi_max=80):
        """清理BMI异常值"""
        cleaned_data = []
        outlier_count = 0

        for item in data:
            start_bmi = item.get('start_bmi')
            end_bmi = item.get('end_bmi')

            # 检查是否为有效BMI值
            if (start_bmi is not None and end_bmi is not None and
                bmi_min <= start_bmi <= bmi_max and
                bmi_min <= end_bmi <= bmi_max):
                cleaned_data.append(item)
            else:
                outlier_count += 1

        print(f"数据清洗完成: 保留 {len(cleaned_data)} 个样本, 移除 {outlier_count} 个异常样本")
        return cleaned_data

    @staticmethod
    def analyze_bmi_distribution(data):
        """分析BMI分布"""
        start_bmis = [item['start_bmi'] for item in data if item['start_bmi'] is not None]
        end_bmis = [item['end_bmi'] for item in data if item['end_bmi'] is not None]
        changes = [end - start for start, end in zip(start_bmis, end_bmis)]

        print(f"BMI分布分析:")
        print(f"  起始BMI: 均值={sum(start_bmis)/len(start_bmis):.1f}, 范围=[{min(start_bmis):.1f}, {max(start_bmis):.1f}]")
        print(f"  结束BMI: 均值={sum(end_bmis)/len(end_bmis):.1f}, 范围=[{min(end_bmis):.1f}, {max(end_bmis):.1f}]")
        print(f"  BMI变化: 均值={sum(changes)/len(changes):.1f}, 范围=[{min(changes):.1f}, {max(changes):.1f}]")


if __name__ == "__main__":
    # 测试模型
    model = create_enhanced_model()

    # 创建测试数据
    batch_size = 4
    test_input = {
        'before_image': torch.randn(batch_size, 3, 160, 160),
        'after_image': torch.randn(batch_size, 3, 160, 160)
    }

    test_targets = {
        'start_bmi': torch.randn(batch_size) * 10 + 25,  # 模拟真实BMI范围
        'end_bmi': torch.randn(batch_size) * 10 + 25,
        'bmi_change': torch.randn(batch_size) * 5
    }

    # 前向传播
    with torch.no_grad():
        predictions = model(test_input)
        loss_info = model.compute_loss(predictions, test_targets)

    print("=== 增强BMI预测模型测试 ===")
    print("\n模型输出形状:")
    for key, value in predictions.items():
        print(f"  {key}: {value.shape}")

    print("\n损失信息:")
    for key, value in loss_info.items():
        if key != 'task_weights':
            print(f"  {key}: {value.item():.4f}")
        else:
            print(f"  {key}: {value.detach().numpy()}")

    print(f"\n模型复杂度:")
    print(f"  总参数数量: {sum(p.numel() for p in model.parameters()):,}")
    print(f"  可训练参数: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")

    # 测试数据清洗功能
    print("\n=== 数据清洗测试 ===")
    test_data = [
        {'start_bmi': 25.0, 'end_bmi': 22.0},
        {'start_bmi': 150.0, 'end_bmi': 30.0},  # 异常值
        {'start_bmi': 30.0, 'end_bmi': 28.0},
        {'start_bmi': 5.0, 'end_bmi': 20.0},    # 异常值
    ]

    cleaner = BMIDataCleaner()
    cleaned_data = cleaner.clean_bmi_data(test_data)
    cleaner.analyze_bmi_distribution(cleaned_data)
