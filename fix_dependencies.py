#!/usr/bin/env python3
"""
Face-BMI 依赖冲突修复脚本
解决TensorFlow版本冲突问题
"""

import subprocess
import sys
import os

def run_command(command, description=""):
    """运行命令并处理错误"""
    print(f"\n🔧 {description}")
    print(f"执行命令: {command}")
    
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True,
            timeout=300
        )
        
        if result.returncode == 0:
            print(f"✅ 成功: {description}")
            if result.stdout.strip():
                print(f"输出: {result.stdout.strip()}")
        else:
            print(f"⚠️ 警告: {description}")
            if result.stderr.strip():
                print(f"错误: {result.stderr.strip()}")
                
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print(f"❌ 超时: {description}")
        return False
    except Exception as e:
        print(f"❌ 异常: {description} - {str(e)}")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"🐍 Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True

def fix_tensorflow_conflicts():
    """修复TensorFlow版本冲突"""
    print("\n🔧 开始修复TensorFlow依赖冲突...")
    
    # 1. 卸载所有TensorFlow相关包
    tensorflow_packages = [
        "tensorflow-intel",
        "tensorflow-cpu", 
        "tensorflow-estimator",
        "tf-keras"
    ]
    
    for package in tensorflow_packages:
        run_command(
            f"pip uninstall {package} -y",
            f"卸载 {package}"
        )
    
    # 2. 清理pip缓存
    run_command("pip cache purge", "清理pip缓存")
    
    # 3. 重新安装TensorFlow 2.19
    success = run_command(
        "pip install tensorflow==2.19.0 --no-cache-dir",
        "安装TensorFlow 2.19.0"
    )
    
    if not success:
        print("❌ TensorFlow安装失败，尝试CPU版本...")
        run_command(
            "pip install tensorflow-cpu==2.19.0 --no-cache-dir",
            "安装TensorFlow CPU版本"
        )
    
    return True

def install_web_dependencies():
    """安装Web应用依赖"""
    print("\n🌐 安装Web应用依赖...")
    
    web_packages = [
        "Flask==2.3.3",
        "Flask-CORS==4.0.0", 
        "gunicorn==23.0.0",
        "Werkzeug==2.3.7"
    ]
    
    for package in web_packages:
        run_command(
            f"pip install {package} --no-deps",
            f"安装 {package}"
        )

def verify_installation():
    """验证安装结果"""
    print("\n🔍 验证安装结果...")
    
    # 检查关键包
    packages_to_check = [
        "tensorflow",
        "keras", 
        "Flask",
        "torch",
        "opencv-python"
    ]
    
    for package in packages_to_check:
        result = subprocess.run(
            f"python -c \"import {package}; print(f'{package}: {{getattr({package}, '__version__', 'unknown')}}')}\"",
            shell=True,
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print(f"✅ {result.stdout.strip()}")
        else:
            print(f"❌ {package}: 导入失败")

def test_face_bmi_imports():
    """测试Face-BMI相关导入"""
    print("\n🧪 测试Face-BMI模块导入...")
    
    test_imports = [
        "import torch",
        "import cv2", 
        "import numpy as np",
        "import tensorflow as tf",
        "from PIL import Image",
        "import flask"
    ]
    
    for import_stmt in test_imports:
        result = subprocess.run(
            f"python -c \"{import_stmt}; print('✅ {import_stmt}')\"",
            shell=True,
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print(result.stdout.strip())
        else:
            print(f"❌ {import_stmt}: 失败")

def main():
    """主函数"""
    print("🚀 Face-BMI 依赖冲突修复工具")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 修复TensorFlow冲突
    fix_tensorflow_conflicts()
    
    # 安装Web依赖
    install_web_dependencies()
    
    # 验证安装
    verify_installation()
    
    # 测试导入
    test_face_bmi_imports()
    
    print("\n🎉 依赖修复完成！")
    print("\n📋 下一步:")
    print("1. 运行: python app.py")
    print("2. 访问: http://localhost:5000")
    print("3. 或运行: ./deploy.sh")

if __name__ == "__main__":
    main()
