"""
Face-BMI Web应用测试版本
用于验证基础环境是否正常
"""

from flask import Flask, request, jsonify, render_template_string
from flask_cors import CORS
import os
import json
from datetime import datetime

# 创建Flask应用
app = Flask(__name__)
CORS(app)

# 配置
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB

# 创建必要目录
os.makedirs('uploads', exist_ok=True)
os.makedirs('results', exist_ok=True)
os.makedirs('static/uploads', exist_ok=True)

@app.route('/')
def index():
    """主页面"""
    return render_template_string('''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Face-BMI 测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .status-card { margin-bottom: 20px; }
        .test-section { margin: 30px 0; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">🧪 Face-BMI 系统测试</h1>
                
                <!-- 系统状态 -->
                <div class="row status-card">
                    <div class="col-md-4">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h5>✅ Flask服务器</h5>
                                <p>正常运行</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h5>🌐 Web界面</h5>
                                <p>加载成功</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-warning text-dark">
                            <div class="card-body text-center">
                                <h5>🔧 模型状态</h5>
                                <p>待测试</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- API测试 -->
                <div class="test-section">
                    <h3>🔍 API测试</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <button class="btn btn-primary w-100" onclick="testHealth()">
                                测试健康检查API
                            </button>
                            <div id="health-result" class="mt-2"></div>
                        </div>
                        <div class="col-md-6">
                            <button class="btn btn-secondary w-100" onclick="testStats()">
                                测试统计API
                            </button>
                            <div id="stats-result" class="mt-2"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 环境信息 -->
                <div class="test-section">
                    <h3>📋 环境信息</h3>
                    <div class="card">
                        <div class="card-body">
                            <div id="env-info">加载中...</div>
                        </div>
                    </div>
                </div>
                
                <!-- 下一步指引 -->
                <div class="test-section">
                    <div class="alert alert-info">
                        <h4>🎯 测试结果</h4>
                        <p><strong>如果您看到这个页面，说明基础Web环境正常！</strong></p>
                        <hr>
                        <h5>下一步操作：</h5>
                        <ol>
                            <li>点击上方按钮测试API功能</li>
                            <li>如果API正常，尝试运行完整版本：<code>python app.py</code></li>
                            <li>如果有错误，查看控制台输出进行调试</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 测试健康检查API
        function testHealth() {
            const resultDiv = document.getElementById('health-result');
            resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm"></div> 测试中...';
            
            fetch('/api/health')
                .then(response => response.json())
                .then(data => {
                    resultDiv.innerHTML = `<div class="alert alert-success">✅ ${JSON.stringify(data)}</div>`;
                })
                .catch(error => {
                    resultDiv.innerHTML = `<div class="alert alert-danger">❌ ${error.message}</div>`;
                });
        }
        
        // 测试统计API
        function testStats() {
            const resultDiv = document.getElementById('stats-result');
            resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm"></div> 测试中...';
            
            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    resultDiv.innerHTML = `<div class="alert alert-success">✅ ${JSON.stringify(data)}</div>`;
                })
                .catch(error => {
                    resultDiv.innerHTML = `<div class="alert alert-danger">❌ ${error.message}</div>`;
                });
        }
        
        // 加载环境信息
        fetch('/api/env')
            .then(response => response.json())
            .then(data => {
                document.getElementById('env-info').innerHTML = `
                    <strong>服务器时间:</strong> ${data.server_time}<br>
                    <strong>Python版本:</strong> ${data.python_version}<br>
                    <strong>Flask版本:</strong> ${data.flask_version}<br>
                    <strong>工作目录:</strong> ${data.working_directory}
                `;
            })
            .catch(error => {
                document.getElementById('env-info').innerHTML = `<span class="text-danger">加载失败: ${error.message}</span>`;
            });
    </script>
</body>
</html>
    ''')

@app.route('/api/health')
def health_check():
    """健康检查API"""
    return jsonify({
        'status': 'healthy',
        'message': 'Face-BMI测试服务器正常运行',
        'timestamp': datetime.now().isoformat(),
        'models_loaded': {
            'bmi_predictor': False,  # 测试版本暂不加载
            'face_detector': False
        }
    })

@app.route('/api/stats')
def get_stats():
    """系统统计API"""
    return jsonify({
        'total_predictions': 0,
        'server_uptime': '刚启动',
        'memory_usage': '未知',
        'disk_usage': '未知'
    })

@app.route('/api/env')
def get_env_info():
    """环境信息API"""
    import sys
    import flask
    
    return jsonify({
        'server_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
        'flask_version': flask.__version__,
        'working_directory': os.getcwd()
    })

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': '页面未找到'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': '服务器内部错误'}), 500

if __name__ == '__main__':
    print("🚀 启动Face-BMI测试服务器...")
    print("📍 访问地址: http://localhost:5000")
    print("🔧 这是测试版本，用于验证基础环境")
    print("-" * 50)
    
    app.run(
        debug=True,
        host='0.0.0.0',
        port=5000,
        threaded=True
    )
